import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import useTranslation from '../hooks/useTranslation'

const SoundCloudPlayer = () => {
  const [isMobile, setIsMobile] = useState(false);
  const [activeTab, setActiveTab] = useState('tracks'); // 'tracks', 'playlists', or 'reposts'
  const { t } = useTranslation();

  // Check if the device is mobile
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    // Initial check
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);

    // Clean up event listener
    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, []);

  // SoundCloud embed URLs for different content types - using black theme
  const embedUrls = {
    tracks: "https://w.soundcloud.com/player/?url=https%3A//api.soundcloud.com/users/1013737636&color=%23000000&auto_play=false&hide_related=false&show_comments=true&show_user=true&show_reposts=false&show_teaser=true",
    playlists: "https://w.soundcloud.com/player/?url=https%3A//soundcloud.com/ayite1210/sets&color=%23000000&auto_play=false&hide_related=false&show_comments=true&show_user=true&show_reposts=false&show_teaser=true"
  };

  return (
    <section id="music" className="soundcloud-section">
      <div className="container">
        <h2>{t('home.music.title')}</h2>
        <div className="soundcloud-container">
          <div className="soundcloud-tabs">
            <button
              className={`soundcloud-tab ${activeTab === 'tracks' ? 'active' : ''}`}
              onClick={() => setActiveTab('tracks')}
            >
              {t('soundcloud.tracks')}
            </button>
            <button
              className={`soundcloud-tab ${activeTab === 'playlists' ? 'active' : ''}`}
              onClick={() => setActiveTab('playlists')}
            >
              {t('soundcloud.playlists')}
            </button>
          </div>

          <div className="soundcloud-player-wrapper">
            <iframe
              width="100%"
              height={isMobile ? "300" : "450"}
              scrolling="no"
              frameBorder="no"
              allow="autoplay"
              src={embedUrls[activeTab]}
              style={{
                border: 'none',
                borderRadius: '4px',
                backgroundColor: '#111'
              }}
            ></iframe>
          </div>

          <div className="soundcloud-cta">
            <p>
              {t('home.music.followText')} <a
                href="https://soundcloud.com/ayite1210"
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  color: '#fff',
                  textDecoration: 'none',
                  borderBottom: '1px solid #fff',
                  paddingBottom: '2px',
                  transition: 'opacity 0.3s ease'
                }}
                onMouseOver={(e) => e.target.style.opacity = '0.7'}
                onMouseOut={(e) => e.target.style.opacity = '1'}
              >
                {t('home.music.platform')}
              </a> {t('home.music.forMoreTracks')}
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}

export default SoundCloudPlayer
