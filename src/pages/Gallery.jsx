import React, { useState } from 'react'
import djProfile from '../assets/images/dj-profile.jpg'
import ayiteProfile from '../assets/images/ayite-profile-new.png'
import ayiteStanding from '../assets/images/aite-standing.jpg'
import useTranslation from '../hooks/useTranslation'

const Gallery = () => {
  const [selectedImage, setSelectedImage] = useState(null)
  const { t } = useTranslation()

  // Gallery images with position information - only profile photos
  const galleryImages = [
    {
      id: 1,
      src: djProfile,
      alt: 'DJ Profile',
      position: 'center top 70%' // Adjusted to better show the face
    },
    {
      id: 2,
      src: ayiteProfile,
      alt: 'Ayite Profile',
      position: 'center top 30%' // Adjusted to better show the face
    },
    {
      id: 3,
      src: ayiteStanding,
      alt: 'Ayite Standing',
      position: 'center center' // This image shows the full body
    }
  ]

  const openModal = (image) => {
    setSelectedImage(image)
    document.body.style.overflow = 'hidden'
  }

  const closeModal = () => {
    setSelectedImage(null)
    document.body.style.overflow = 'auto'
  }

  return (
    <div style={{ paddingTop: '120px' }}>
      <section>
        <div className="container">
          <h2>{t('gallery.title')}</h2>

          <div className="gallery" style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
            gap: '30px',
            marginBottom: '40px'
          }}>
            {galleryImages.map((image) => (
              <div
                key={image.id}
                className="gallery-item"
                style={{
                  backgroundImage: `url(${image.src})`,
                  cursor: 'pointer',
                  height: '300px',
                  backgroundSize: 'cover',
                  backgroundPosition: image.position || 'center', // Use the position information
                  borderRadius: '4px',
                  boxShadow: '0 10px 20px rgba(0, 0, 0, 0.3)',
                  transition: 'transform 0.3s ease, box-shadow 0.3s ease'
                }}
                onClick={() => openModal(image)}
              ></div>
            ))}
          </div>
        </div>
      </section>

      {selectedImage && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.9)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1000,
            padding: '20px'
          }}
          onClick={closeModal}
        >
          <div
            style={{
              position: 'relative',
              maxWidth: '90%',
              maxHeight: '90%'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <button
              style={{
                position: 'absolute',
                top: '-40px',
                right: 0,
                background: 'none',
                border: 'none',
                color: 'white',
                fontSize: '30px',
                cursor: 'pointer',
                padding: 0
              }}
              onClick={closeModal}
            >
              {t('gallery.closeButton')}
            </button>
            {selectedImage.src ? (
              <img
                src={selectedImage.src}
                alt={selectedImage.alt}
                style={{
                  maxWidth: '100%',
                  maxHeight: '80vh',
                  display: 'block'
                }}
              />
            ) : (
              <div
                style={{
                  width: '80vw',
                  height: '80vh',
                  backgroundColor: selectedImage.color,
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  color: '#fff'
                }}
              >
                <p>{t('gallery.placeholder')}</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default Gallery
