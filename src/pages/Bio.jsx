import React from 'react'
import ayiteProfile from '../assets/images/ayite-profile-new.png'
import useTranslation from '../hooks/useTranslation'

const Bio = () => {
  const { t } = useTranslation()

  return (
    <div style={{ paddingTop: '120px' }}>
      <section>
        <div className="container">
          <h2>{t('bio.title')}</h2>
          <div className="bio-content">
            <div className="bio-image" style={{ backgroundImage: `url(${ayiteProfile})` }}></div>
            <div className="bio-text">
              <p>{t('bio.paragraph1')}</p>
              <p>{t('bio.paragraph2')}</p>
             #<p>{t('bio.paragraph3')}</p>
              <p>{t('bio.paragraph4')}</p>
              <p>{t('bio.paragraph5')}</p>
              <p>{t('bio.paragraph6')}</p>
              <p>{t('bio.paragraph7')}</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Bio
