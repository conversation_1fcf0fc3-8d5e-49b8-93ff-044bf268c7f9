import React from 'react'
import { Link } from 'react-router-dom'
import djProfile from '../assets/images/dj-profile.jpg'
import ayiteProfile from '../assets/images/ayite-profile-new.png'
import ayiteStanding from '../assets/images/aite-standing.jpg'
import SoundCloudPlayer from '../components/SoundCloudPlayer'
import XceedEvents from '../components/XceedEvents'
import useTranslation from '../hooks/useTranslation'

const Home = () => {
  const { t } = useTranslation();

  return (
    <div>
      {/* Hero Section */}
      <section className="hero" style={{
          backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url(${djProfile})`,
          backgroundPosition: 'center', /* Further adjusted to move image down by 20% more */
          backgroundRepeat: 'no-repeat'
        }}>
        <div className="container hero-content">
          <h1>{t('home.hero.title')}</h1>
          <p>{t('home.hero.subtitle')}</p>
        </div>
      </section>

      {/* Bio Section */}
      <section id="bio">
        <div className="container">
          <h2>{t('home.bio.title')}</h2>
          <div className="bio-content">
            <div className="bio-image" style={{
              backgroundImage: `url(${ayiteProfile})`,
              backgroundPosition: 'center top 30%' /* Adjusted to better show the face */
            }}></div>
            <div className="bio-text">
              <p>{t('home.bio.paragraph1')}</p>
              <Link to="/bio">
                <button>{t('home.bio.readMore')}</button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Events Section */}
      <section>
        <div className="container">
          <h2>{t('home.events.title')}</h2>
          <XceedEvents />
        </div>
      </section>

      {/* SoundCloud Player */}
      <SoundCloudPlayer />

      {/* Gallery Section */}
      <section id="gallery">
        <div className="container">
          <h2>{t('home.gallery.title')}</h2>
          <div className="gallery">
            <div className="gallery-item" style={{
              backgroundImage: `url(${djProfile})`,
              backgroundPosition: 'center top 70%' /* Adjusted to better show the face */
            }}></div>
            <div className="gallery-item" style={{
              backgroundImage: `url(${ayiteProfile})`,
              backgroundPosition: 'center top 30%' /* Adjusted to better show the face */
            }}></div>
            <div className="gallery-item" style={{
              backgroundImage: `url(${ayiteStanding})`,
              backgroundPosition: 'center center' /* This image shows the full body */
            }}></div>
          </div>
          <Link to="/gallery">
            <button style={{ marginTop: '30px' }}>{t('home.gallery.viewAll')}</button>
          </Link>
        </div>
      </section>

      {/* Booking Section */}
      <section id="booking">
        <div className="container">
          <h2>{t('home.booking.title')}</h2>
          <div className="booking-form">
            <p style={{ marginBottom: '20px', textAlign: 'center' }}>
              {t('home.booking.text')}
            </p>
            <div style={{ textAlign: 'center', display: 'flex', justifyContent: 'center', gap: '20px' }}>
              <a
                href="https://www.instagram.com/ayite__/"
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  padding: '10px 20px',
                  backgroundColor: '#222',
                  color: '#fff',
                  textDecoration: 'none',
                  borderRadius: '4px',
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease'
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '8px' }}>
                  <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                  <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                </svg>
                Instagram
              </a>
              <Link to="/booking">
                <button>{t('home.booking.button')}</button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Home
