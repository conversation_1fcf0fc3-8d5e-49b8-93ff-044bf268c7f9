import React, { useState, useEffect } from 'react'
import useTranslation from '../hooks/useTranslation'
import { fetchYoutubeShorts } from '../utils/api'

const Video = () => {
  const { t } = useTranslation();
  const [shorts, setShorts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isMobile, setIsMobile] = useState(false);

  // Check if the device is mobile
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    // Initial check
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);

    // Clean up event listener
    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, []);

  useEffect(() => {
    const getYoutubeShorts = async () => {
      try {
        setLoading(true);
        const shortsData = await fetchYoutubeShorts();
        setShorts(shortsData);
      } catch (error) {
        console.error('Error fetching YouTube shorts:', error);
      } finally {
        setLoading(false);
      }
    };

    getYoutubeShorts();
  }, []);

  return (
    <div style={{ paddingTop: '120px' }}>
      <section className="video-section">
        <div className="container">
          {/* Featured Video */}
          <h2 style={{ marginTop: '60px' }}>{t('video.featuredVideo')}</h2>
          <div className="video-container">
            <iframe
              width="100%"
              height="450"
              src="https://www.youtube.com/embed/pXMNNxojqHc?start=2547"
              title="AYITE YouTube Video"
              style={{
                border: 'none',
                borderRadius: '12px',
                boxShadow: '0 10px 30px rgba(0, 0, 0, 0.5)'
              }}
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowFullScreen
            ></iframe>
          </div>

          {/* YouTube Shorts */}
          <h2 style={{ marginTop: '60px' }}>{t('video.youtubeShorts')}</h2>

          {loading ? (
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <p>Loading YouTube shorts...</p>
            </div>
          ) : (
            <div className="shorts-container" style={{
              display: 'flex',
              flexWrap: 'wrap',
              justifyContent: 'center',
              gap: '20px'
            }}>
              {shorts.length > 0 ? (
                (isMobile ? shorts.slice(0, 2) : shorts).map((short, index) => (
                  <div className="shorts-item" key={short.id}>
                    <iframe
                      width="315"
                      height="560"
                      src={short.embedUrl}
                      title={`AYITE YouTube Short ${index + 1}`}
                      style={{
                        border: 'none',
                        borderRadius: '12px',
                        boxShadow: '0 10px 30px rgba(0, 0, 0, 0.5)'
                      }}
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                      allowFullScreen
                    ></iframe>
                  </div>
                ))
              ) : (
                <div style={{ textAlign: 'center', padding: '20px' }}>
                  <p>No YouTube shorts available at the moment.</p>
                </div>
              )}
            </div>
          )}

          {/* YouTube Playlist */}
          <h2 style={{ marginTop: '60px' }}>{t('video.youtubePlaylist')}</h2>
          <div className="video-container">
            <iframe
              width="100%"
              height="450"
              src="https://www.youtube.com/embed/videoseries?list=PLandQwc5LewqWTM934IHUJaZlDjn_uBvH"
              title="AYITE YouTube Playlist"
              style={{
                border: 'none',
                borderRadius: '12px',
                boxShadow: '0 10px 30px rgba(0, 0, 0, 0.5)'
              }}
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowFullScreen
            ></iframe>
          </div>

          <div className="youtube-cta">
            <a
              href="https://www.youtube.com/@AYITE_ofc"
              target="_blank"
              rel="noopener noreferrer"
              className="youtube-button"
            >
              {t('video.subscribeButton')}
            </a>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Video
