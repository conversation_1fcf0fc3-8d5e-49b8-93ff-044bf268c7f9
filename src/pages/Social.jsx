import React, { useState, useEffect } from 'react'
import { FaInstagram, FaTiktok, FaYoutube, FaSpotify } from 'react-icons/fa'
import { SiSoundcloud } from 'react-icons/si'
import useTranslation from '../hooks/useTranslation'

const Social = () => {
  const [activeTab, setActiveTab] = useState('all'); // 'all', 'instagram', 'tiktok', or 'youtube'
  const [isMobile, setIsMobile] = useState(false);
  const { t } = useTranslation();

  // Check if the device is mobile
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    // Initial check
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);

    // Clean up event listener
    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, []);

  // Social media data
  const socialMedia = [
    {
      id: 'instagram',
      name: 'Instagram',
      handle: '@ayite__',
      url: 'https://www.instagram.com/ayite__/',
      embedUrl: 'https://www.instagram.com/ayite__/embed',
      icon: <FaInstagram />,
      ctaTextKey: 'social.followInstagram'
    },
    {
      id: 'tiktok',
      name: 'TikTok',
      handle: '@ayite.music',
      url: 'https://www.tiktok.com/@ayite.music',
      embedUrl: 'https://www.tiktok.com/embed/@ayite.music',
      icon: <FaTiktok />,
      ctaTextKey: 'social.followTikTok'
    },
    {
      id: 'youtube',
      name: 'YouTube',
      handle: '@AYITE_ofc',
      url: 'https://www.youtube.com/@AYITE_ofc',
      embedUrl: 'https://www.youtube.com/embed/pXMNNxojqHc',
      icon: <FaYoutube />,
      ctaTextKey: 'social.subscribeYouTube'
    },
    {
      id: 'spotify',
      name: 'Spotify',
      handle: 'AYITE',
      url: 'https://open.spotify.com/artist/4YourSpotifyArtistId',
      embedUrl: 'https://open.spotify.com/embed/artist/4YourSpotifyArtistId',
      icon: <FaSpotify />,
      ctaTextKey: 'social.followSpotify'
    },
    {
      id: 'soundcloud',
      name: 'SoundCloud',
      handle: 'ayite1210',
      url: 'https://soundcloud.com/ayite1210',
      embedUrl: 'https://w.soundcloud.com/player/?url=https%3A//api.soundcloud.com/users/1013737636&color=%23000000&auto_play=false&hide_related=false&show_comments=true&show_user=true&show_reposts=false&show_teaser=true',
      icon: <SiSoundcloud />,
      ctaTextKey: 'social.followSoundCloud'
    }
  ];

  // Filter social media based on active tab
  const filteredSocialMedia = activeTab === 'all'
    ? socialMedia
    : socialMedia.filter(item => item.id === activeTab);

  return (
    <div className="social-page" style={{ paddingTop: '120px' }}>
      <section className="social-section">
        <div className="container">
          <h2>{t('social.title')}</h2>

          <div className="social-tabs">
            <button
              className={`social-tab ${activeTab === 'all' ? 'active' : ''}`}
              onClick={() => setActiveTab('all')}
            >
              {t('social.allPlatforms')}
            </button>

            {socialMedia.map(platform => (
              <button
                key={platform.id}
                className={`social-tab ${activeTab === platform.id ? 'active' : ''}`}
                onClick={() => setActiveTab(platform.id)}
              >
                <span className="social-tab-icon">{platform.icon}</span>
                <span>{platform.name}</span>
              </button>
            ))}
          </div>

          <div className="social-feeds-large">
            {filteredSocialMedia.map(platform => (
              <div key={platform.id} className="social-feed-large">
                <h3>
                  <span className="social-feed-icon">{platform.icon}</span>
                  <span>{platform.name} - {platform.handle}</span>
                </h3>

                <div className="social-embed-container-large">
                  {platform.id === 'instagram' && (
                    <iframe
                      src={platform.embedUrl}
                      width="100%"
                      height={isMobile ? "450" : "600"}
                      style={{
                        border: 'none',
                        overflow: 'hidden',
                        borderRadius: '8px',
                        backgroundColor: '#000'
                      }}
                      title={`${platform.name} Feed for ${platform.handle}`}
                      loading="lazy"
                    ></iframe>
                  )}

                  {platform.id === 'tiktok' && (
                    <div style={{ height: isMobile ? "450px" : "600px", overflow: 'hidden', borderRadius: '8px', position: 'relative' }}>
                      <iframe
                        src={platform.embedUrl}
                        style={{
                          width: '100%',
                          height: isMobile ? "550px" : "700px",
                          border: 'none',
                          position: 'absolute',
                          top: '0',
                          left: '0',
                          borderRadius: '8px',
                          backgroundColor: '#000'
                        }}
                        title={`${platform.name} Feed for ${platform.handle}`}
                        loading="lazy"
                      ></iframe>
                    </div>
                  )}

                  {platform.id === 'youtube' && (
                    <div className="youtube-embed-wrapper">
                      <iframe
                        width="100%"
                        height={isMobile ? "250" : "400"}
                        src={platform.embedUrl}
                        title={`${platform.name} Feed for ${platform.handle}`}
                        style={{
                          border: 'none',
                          borderRadius: '8px',
                          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.5)'
                        }}
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                        allowFullScreen
                        loading="lazy"
                      ></iframe>
                      <div className="youtube-channel-info" style={{ marginTop: '15px', textAlign: 'center' }}>
                        <p style={{ fontSize: '0.9rem', opacity: '0.8', margin: '5px 0' }}>
                          Latest Video Preview
                        </p>
                        <div style={{ display: 'flex', justifyContent: 'center', gap: '10px', marginTop: '10px' }}>
                          <a
                            href="https://www.youtube.com/@AYITE_ofc/videos"
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{
                              padding: '5px 10px',
                              backgroundColor: 'rgba(255, 255, 255, 0.1)',
                              borderRadius: '4px',
                              fontSize: '0.8rem'
                            }}
                          >
                            {t('social.videos')}
                          </a>
                          <a
                            href="https://www.youtube.com/@AYITE_ofc/shorts"
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{
                              padding: '5px 10px',
                              backgroundColor: 'rgba(255, 255, 255, 0.1)',
                              borderRadius: '4px',
                              fontSize: '0.8rem'
                            }}
                          >
                            {t('social.shorts')}
                          </a>
                          <a
                            href="https://www.youtube.com/@AYITE_ofc/playlists"
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{
                              padding: '5px 10px',
                              backgroundColor: 'rgba(255, 255, 255, 0.1)',
                              borderRadius: '4px',
                              fontSize: '0.8rem'
                            }}
                          >
                            {t('social.playlists')}
                          </a>
                        </div>
                      </div>
                    </div>
                  )}

                  {platform.id === 'spotify' && (
                    <iframe
                      src={platform.embedUrl}
                      width="100%"
                      height={isMobile ? "300" : "400"}
                      style={{
                        border: 'none',
                        borderRadius: '8px',
                        backgroundColor: '#000'
                      }}
                      title={`${platform.name} Profile for ${platform.handle}`}
                      loading="lazy"
                      allow="autoplay; clipboard-write; encrypted-media; fullscreen; picture-in-picture"
                    ></iframe>
                  )}

                  {platform.id === 'soundcloud' && (
                    <iframe
                      src={platform.embedUrl}
                      width="100%"
                      height={isMobile ? "300" : "400"}
                      style={{
                        border: 'none',
                        borderRadius: '8px',
                        backgroundColor: '#000'
                      }}
                      title={`${platform.name} Profile for ${platform.handle}`}
                      loading="lazy"
                      scrolling="no"
                      frameBorder="no"
                      allow="autoplay"
                    ></iframe>
                  )}
                </div>

                <div className="social-link-container-large">
                  <a
                    href={platform.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="social-link-large"
                  >
                    {t(platform.ctaTextKey)}
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}

export default Social
