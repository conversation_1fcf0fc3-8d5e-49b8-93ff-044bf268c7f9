/* Global Styles */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Montserrat', sans-serif;
}

body {
  background-color: #000;
  color: #fff;
  line-height: 1.6;
  overflow-x: hidden;
}

a {
  color: #fff;
  text-decoration: none;
  transition: opacity 0.3s ease;
}

a:hover {
  opacity: 0.7;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header */
header {
  padding: 30px 0;
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 100;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 2rem;
  font-weight: 700;
  letter-spacing: 2px;
}

nav ul {
  display: flex;
  list-style: none;
}

nav ul li {
  margin-left: 30px;
}

@media (max-width: 768px) {
  .mobile-menu-icon {
    display: block !important;
    cursor: pointer;
    z-index: 1000;
  }

  nav {
    position: fixed;
    top: 0;
    right: -100%;
    width: 70%;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.95);
    transition: right 0.3s ease;
    z-index: 99;
    overflow-y: auto;
  }

  nav.active {
    right: 0;
  }

  .mobile-menu-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    padding: 60px 0 30px;
  }

  nav ul {
    flex-direction: column;
    align-items: center;
  }

  nav ul li {
    margin: 15px 0;
  }

  nav ul.active {
    display: flex;
  }
}

/* Hero Section */
.hero {
  height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), #111;
  margin-bottom: 60px;
  padding-top: 120px; /* Account for the header's position */
  background-position: center top 90% !important; /* Further adjusted to move image down by 20% more */
}

.hero-content {
  max-width: 800px;
}

.hero h1 {
  font-size: 4rem;
  margin-bottom: 20px;
  line-height: 1.2;
}

.hero p {
  font-size: 1.2rem;
  margin-bottom: 30px;
}

/* Section Styles */
section {
  padding: 80px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

section h2 {
  font-size: 2.5rem;
  margin-bottom: 40px;
  position: relative;
  display: inline-block;
}

section h2:after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -10px;
  width: 60px;
  height: 3px;
  background-color: #fff;
}

/* Bio Section */
.bio-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  align-items: center;
}

.bio-image {
  width: 100%;
  height: 500px;
  background-color: #1a1a1a;
  background-size: cover;
  background-position: center;
}

.bio-text p {
  font-weight: 300;
  line-height: 1.8;
  margin-bottom: 20px;
  letter-spacing: 0.3px;
}

/* Shows Section */
.shows-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
}

.show-card {
  background-color: #111;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.show-card h3 {
  margin-bottom: 10px;
}

.show-date {
  color: #888;
  margin-bottom: 15px;
  display: block;
}

/* Gallery Section */
.gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.gallery-item {
  height: 250px;
  background-color: #1a1a1a;
  background-size: cover;
  background-position: center;
}

/* SoundCloud Player Section */
.soundcloud-section {
  background-color: #0a0a0a;
  padding: 80px 0;
  position: relative;
}

.soundcloud-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(0,0,0,0.9) 0%, rgba(20,20,20,0.8) 100%);
  z-index: 0;
}

.soundcloud-section .container {
  position: relative;
  z-index: 1;
}

.soundcloud-container {
  margin-top: 40px;
}

.soundcloud-player-wrapper {
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.soundcloud-player-wrapper:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.7);
}

.soundcloud-cta {
  margin-top: 25px;
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.soundcloud-cta:hover {
  opacity: 1;
}

.soundcloud-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 25px;
  gap: 20px;
}

.soundcloud-tab {
  background: transparent;
  color: #fff;
  border: 1px solid #fff;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.soundcloud-tab:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.soundcloud-tab.active {
  background-color: #fff;
  color: #000;
}

/* Social Feed Section */
.social-feeds {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 40px;
}

.social-feed {
  background-color: #111;
  padding: 20px;
  height: 450px;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.social-feed:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

.social-feed h3 {
  margin-bottom: 15px;
  font-size: 1.2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 10px;
  display: flex;
  align-items: center;
}

.social-section {
  background-color: #0a0a0a;
  position: relative;
}

.social-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(10,10,10,0.95) 0%, rgba(20,20,20,0.9) 100%);
  z-index: 0;
}

.social-section .container {
  position: relative;
  z-index: 1;
}

.social-embed-container {
  margin: 15px 0;
  border-radius: 4px;
  overflow: hidden;
  background-color: #000;
}

.social-link-container {
  text-align: center;
  margin-top: 15px;
}

.social-link {
  display: inline-block;
  padding: 8px 16px;
  background-color: #222;
  color: #fff;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.social-link:hover {
  background-color: #333;
  transform: translateY(-2px);
}

.view-all-container {
  text-align: center;
  margin-top: 40px;
}

.view-all-button {
  padding: 12px 30px;
  font-size: 1rem;
  background-color: #fff;
  color: #000;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-all-button:hover {
  background-color: #eee;
  transform: translateY(-2px);
}

/* Social Page Styles */
.social-page {
  background-color: #0a0a0a;
}

.social-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  gap: 20px;
  flex-wrap: wrap;
}

.social-tab {
  background: transparent;
  color: #fff;
  border: 1px solid #fff;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.social-tab:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.social-tab.active {
  background-color: #fff;
  color: #000;
}

.social-tab-icon {
  display: flex;
  align-items: center;
}

.social-feeds-large {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.social-feed-large {
  background-color: #111;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.social-feed-large:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.5);
}

.social-feed-large h3 {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
  font-size: 1.3rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 15px;
}

.social-feed-icon {
  font-size: 1.5rem;
}

.social-embed-container-large {
  margin: 20px 0;
  border-radius: 8px;
  overflow: hidden;
  background-color: #000;
}

.social-link-container-large {
  text-align: center;
  margin-top: 20px;
}

.social-link-large {
  display: inline-block;
  padding: 10px 20px;
  background-color: #222;
  color: #fff;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 500;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.social-link-large:hover {
  background-color: #333;
  transform: translateY(-2px);
}

/* Language Selector Styles */
.language-selector-container {
  margin-left: 20px;
  display: flex;
  align-items: center;
  position: relative;
}

.language-dropdown {
  position: relative;
}

.language-dropdown-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  background: transparent;
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.language-dropdown-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.globe-icon {
  font-size: 0.9rem;
}

.dropdown-arrow {
  font-size: 0.7rem;
  transition: transform 0.3s ease;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

.language-dropdown-menu {
  position: absolute;
  top: calc(100% + 5px);
  right: 0;
  background-color: #111;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  min-width: 120px;
  overflow: hidden;
}

.language-option {
  display: block;
  width: 100%;
  text-align: left;
  padding: 8px 12px;
  background: transparent;
  color: #fff;
  border: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.language-option:last-child {
  border-bottom: none;
}

.language-option:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.language-option.active {
  background-color: rgba(255, 255, 255, 0.05);
  font-weight: 500;
}

.mobile-language-selector {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  padding: 20px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Video Page Styles */
.video-section {
  padding: 40px 0;
}

.video-container {
  max-width: 800px;
  margin: 0 auto 40px auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.video-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.7);
}

.youtube-cta {
  text-align: center;
  margin-top: 40px;
}

.youtube-button {
  display: inline-block;
  padding: 12px 30px;
  background-color: #fff;
  color: #000;
  font-weight: bold;
  border-radius: 4px;
  text-decoration: none;
  transition: all 0.3s ease;
}

.youtube-button:hover {
  background-color: #f00;
  color: #fff;
  transform: translateY(-2px);
}

/* YouTube Shorts Styles */
.shorts-container {
  margin: 30px 0;
}

.shorts-item {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.shorts-item:hover {
  transform: translateY(-5px);
}

/* Mobile styles for shorts */
@media (max-width: 768px) {
  .shorts-container {
    flex-direction: column;
    align-items: center;
  }

  .shorts-item iframe {
    width: 280px;
    height: 500px;
  }
}

/* Booking Section */
.booking-form {
  max-width: 600px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 10px;
  background-color: #111;
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #fff;
}

.form-group textarea {
  height: 150px;
}

button {
  background-color: #fff;
  color: #000;
  border: none;
  padding: 12px 30px;
  cursor: pointer;
  font-weight: 600;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

button:hover {
  background-color: #ddd;
}

/* Footer */
footer {
  padding: 60px 0 30px;
  background-color: rgba(0, 0, 0, 0.3);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 60px;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 40px;
}

.footer-section {
  flex: 1;
  min-width: 250px;
  margin-bottom: 30px;
}

.footer-section h3 {
  font-size: 1.2rem;
  margin-bottom: 20px;
  position: relative;
  display: inline-block;
}

.footer-section h3:after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -8px;
  width: 40px;
  height: 2px;
  background-color: #fff;
}

.social-links {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 15px;
}

.social-links a {
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: transform 0.3s ease;
}

.social-links a:hover {
  transform: translateX(5px);
}

.legal-links {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 15px;
}

.legal-links a {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.legal-links a:hover {
  transform: translateX(5px);
}

.footer-bottom {
  padding-top: 30px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-bottom p {
  margin: 0;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
}

.created-by {
  display: flex;
  align-items: center;
  gap: 5px;
}

.created-by a {
  color: #fff;
  font-weight: 500;
  transition: color 0.3s ease;
}

.created-by a:hover {
  color: #ddd;
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }

  .bio-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .bio-image {
    height: 250px;
  }

  /* Mobile optimizations for SoundCloud player */
  .soundcloud-section {
    padding: 60px 0;
  }

  .soundcloud-container {
    margin-top: 25px;
  }

  .soundcloud-tabs {
    gap: 10px;
    margin-bottom: 15px;
  }

  .soundcloud-tab {
    padding: 8px 12px;
    font-size: 0.8rem;
  }

  /* Mobile optimizations for social feeds */
  .social-feeds {
    grid-template-columns: 1fr;
    gap: 25px;
    margin-top: 25px;
  }

  .social-feed {
    height: 400px;
    padding: 15px;
  }

  .social-feed h3 {
    font-size: 1.1rem;
    margin-bottom: 12px;
    padding-bottom: 8px;
  }

  .social-embed-container {
    margin: 10px 0;
  }

  .social-link-container {
    margin-top: 12px;
  }

  .social-link {
    padding: 6px 14px;
    font-size: 0.85rem;
  }

  .view-all-container {
    margin-top: 30px;
  }

  .view-all-button {
    padding: 10px 25px;
    font-size: 0.9rem;
  }

  /* Mobile optimizations for Social page */
  .social-tabs {
    gap: 10px;
    margin-bottom: 20px;
  }

  .social-tab {
    padding: 8px 12px;
    font-size: 0.8rem;
  }

  .social-feeds-large {
    grid-template-columns: 1fr;
    gap: 25px;
    margin-top: 25px;
  }

  .social-feed-large {
    padding: 20px;
  }

  .social-feed-large h3 {
    font-size: 1.1rem;
    margin-bottom: 15px;
    padding-bottom: 10px;
  }

  .social-feed-icon {
    font-size: 1.2rem;
  }

  .social-embed-container-large {
    margin: 15px 0;
  }

  .social-link-large {
    padding: 8px 16px;
    font-size: 0.9rem;
  }

  .shows-grid {
    grid-template-columns: 1fr;
  }

  .gallery {
    grid-template-columns: repeat(2, 1fr);
  }

  .hero {
    background-position: center top 80% !important; /* Further adjusted for mobile to move image down by 20% more */
  }

  /* Hide the event banner on mobile */
  .event-banner {
    display: none;
  }

  .hero h1 {
    font-size: 2.5rem;
  }

  .hero p {
    font-size: 1rem;
  }

  section {
    padding: 60px 0;
  }

  section h2 {
    font-size: 2rem;
  }

  .booking-form {
    width: 100%;
  }

  .event-banner p {
    font-size: 0.8rem;
  }

  /* Language dropdown mobile optimizations */
  .language-dropdown-toggle {
    padding: 5px 10px;
    font-size: 0.8rem;
  }

  .current-language {
    display: none; /* Hide the language name on mobile, just show the icon */
  }

  .language-dropdown-menu {
    min-width: 100px;
    right: -10px;
  }

  .language-option {
    padding: 6px 10px;
    font-size: 0.8rem;
  }

  /* Footer mobile optimizations */
  footer {
    padding: 40px 0 20px;
    margin-top: 40px;
  }

  .footer-content {
    flex-direction: column;
    margin-bottom: 30px;
  }

  .footer-section {
    width: 100%;
    margin-bottom: 30px;
    text-align: center;
  }

  .footer-section h3 {
    font-size: 1.1rem;
    margin-bottom: 15px;
  }

  .footer-section h3:after {
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
  }

  .social-links, .legal-links {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 10px;
  }

  .social-links a {
    font-size: 0.9rem;
    padding: 8px 12px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    margin: 5px;
  }

  .social-links a:hover, .legal-links a:hover {
    transform: translateY(-3px);
  }

  .legal-links a {
    padding: 8px 12px;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    margin: 5px;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .created-by {
    justify-content: center;
  }

  /* Video page mobile optimizations */
  iframe {
    height: 250px !important;
  }

  /* Exception for social feed iframes */
  .social-feed iframe {
    height: auto !important;
  }

  /* Specific heights for different social embeds */
  .social-feed:nth-child(1) iframe {
    height: 300px !important;
  }

  .social-feed:nth-child(2) iframe {
    height: 400px !important;
  }

  .social-feed:nth-child(3) iframe {
    height: 200px !important;
  }
}

/* Event Banner */
.event-banner {
  background-color: #111;
  padding: 10px 0;
  text-align: center;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 101;
}

.event-banner p {
  margin: 0;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 10px;
}

header {
  top: 120px; /* Account for event banner (40px) + SoundCloud player (80px) */
}

/* Mobile optimizations for event banner */
@media (max-width: 768px) {
  .event-banner {
    padding: 5px 0;
    font-size: 0.8rem;
  }

  .event-banner p {
    font-size: 0.8rem;
    gap: 5px;
  }

  .event-banner p span {
    font-size: 0.75rem;
  }

  .event-banner a {
    padding: 3px 8px !important;
    font-size: 0.7rem !important;
  }

  header {
    top: 0; /* No event banner on mobile, so header goes to the top */
  }
}
