# AYITE DJ Backend

This is the backend service for the AYITE DJ website. It serves as a proxy for the Xceed API to avoid CORS issues in the frontend.

## Features

- Proxy for Xceed API
- CORS handling
- Error handling with fallback data
- Ready for Vercel deployment

## Setup

1. Install dependencies:
   ```
   npm install
   ```

2. Create a `.env` file with the following content:
   ```
   PORT=3001
   NODE_ENV=development
   ```

3. Start the development server:
   ```
   npm run dev
   ```

## API Endpoints

- `GET /api/health` - Health check endpoint
- `GET /api/xceed/events` - Get all events (both upcoming and past)
- `GET /api/xceed/events/:id` - Get a specific event by ID

## Deployment

This backend is configured for deployment on Vercel. The `vercel.json` file contains the necessary configuration.

To deploy:

1. Install Vercel CLI:
   ```
   npm install -g vercel
   ```

2. Deploy:
   ```
   vercel
   ```

## Development

- `npm run dev` - Start the development server with hot reloading
- `npm start` - Start the production server
