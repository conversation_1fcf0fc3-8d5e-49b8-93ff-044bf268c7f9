const express = require('express');
const cors = require('cors');
const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// CORS configuration
app.use(cors({
  origin: '*', // Allow all origins
  methods: ['GET', 'POST', 'OPTIONS'],
  credentials: true,
  optionsSuccessStatus: 200 // Some legacy browsers (IE11) choke on 204
}));

// Add CORS headers to all responses as a backup
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, X-CSRF-Token, Accept-Version, Content-Length, Content-MD5, Date, X-Api-Version');
  res.header('Access-Control-Allow-Credentials', 'true');

  // Log request details for debugging
  console.log('Request received:', {
    method: req.method,
    url: req.url,
    headers: req.headers
  });

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    console.log('Handling OPTIONS request');
    return res.status(200).end();
  }

  next();
});

app.use(express.json());

// Root endpoint
app.get('/', (req, res) => {
  console.log('Root endpoint accessed');
  res.status(200).json({
    message: 'AYITE DJ Backend API',
    version: '1.0.1',
    timestamp: new Date().toISOString()
  });
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  console.log('API health endpoint accessed');
  res.status(200).json({
    status: 'ok',
    message: 'Backend service is running',
    version: '1.0.1',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Root health check endpoint (for direct access)
app.get('/health', (req, res) => {
  console.log('Root health endpoint accessed');
  res.status(200).json({
    status: 'ok',
    message: 'Backend service is running',
    version: '1.0.1',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Proxy endpoint for all Xceed events (both upcoming and past)
app.get('/api/xceed/events', async (req, res) => {
  try {
    // Get current timestamp in seconds
    const currentTimestamp = Math.floor(Date.now() / 1000);

    // Artist ID for AYITE
    const artistId = 'd008c412-e3df-11ef-83ae-0242ac11000f';

    // Common headers for Xceed API
    const headers = {
      'accept': '*/*',
      'accept-language': 'en',
      'cache-control': 'no-cache',
      'content-type': 'application/json',
      'origin': 'https://xceed.me',
      'pragma': 'no-cache',
      'user-agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36'
    };

    // Fetch upcoming events
    const upcomingEventsResponse = await axios.get('https://events.xceed.me/v1/events', {
      params: {
        orderBy: 'date',
        sort: 'ASC',
        offset: 0,
        artists: artistId,
        limit: 10,
        startTime: currentTimestamp
      },
      headers
    });

    // Fetch past events
    const pastEventsResponse = await axios.get('https://events.xceed.me/v1/events', {
      params: {
        orderBy: 'date',
        sort: 'DESC',
        offset: 0,
        artists: artistId,
        endTime: currentTimestamp,
        limit: 10
      },
      headers
    });

    // Combine the results
    const allEvents = [
      ...upcomingEventsResponse.data.data,
      ...pastEventsResponse.data.data
    ];

    res.json(allEvents);
  } catch (error) {
    console.error('Error fetching Xceed events:', error);
    res.status(500).json({
      error: 'Failed to fetch events from Xceed',
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Proxy endpoint for a specific Xceed event
app.get('/api/xceed/events/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Common headers for Xceed API
    const headers = {
      'accept': '*/*',
      'accept-language': 'en',
      'cache-control': 'no-cache',
      'content-type': 'application/json',
      'origin': 'https://xceed.me',
      'pragma': 'no-cache',
      'user-agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36'
    };

    const response = await axios.get(`https://events.xceed.me/v1/events/${id}`, {
      headers
    });

    res.json(response.data.data);
  } catch (error) {
    console.error(`Error fetching Xceed event ${req.params.id}:`, error);
    res.status(500).json({
      error: 'Failed to fetch event details from Xceed',
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Endpoint to fetch latest YouTube shorts from AYITE's channel
app.get('/api/youtube/shorts', async (req, res) => {
  try {
    // YouTube channel ID for AYITE
    const channelId = 'AYITE_ofc';

    // We'll use a scraping approach since we don't have a YouTube API key
    const response = await axios.get(`https://www.youtube.com/@${channelId}/shorts`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html',
        'Accept-Language': 'en-US,en;q=0.9'
      }
    });

    // Extract video IDs from the HTML response
    const html = response.data;
    const videoIdRegex = /(?:shorts|watch\?v=)\/([a-zA-Z0-9_-]{11})/g;
    const matches = html.match(videoIdRegex) || [];

    // Extract unique video IDs
    const videoIds = [...new Set(
      matches.map(match => {
        // Extract the video ID from the match
        const idMatch = /(?:shorts|watch\?v=)\/([a-zA-Z0-9_-]{11})/.exec(match);
        return idMatch ? idMatch[1] : null;
      }).filter(id => id) // Remove null values
    )];

    // Return the first 3 video IDs (or fewer if less are available)
    const latestShorts = videoIds.slice(0, 3).map(id => ({
      id,
      embedUrl: `https://www.youtube.com/embed/${id}`
    }));

    res.json({
      shorts: latestShorts,
      total: latestShorts.length
    });
  } catch (error) {
    console.error('Error fetching YouTube shorts:', error);

    // Fallback data in case of error
    const fallbackShorts = [
      { id: 'eLtphwetAzw', embedUrl: 'https://www.youtube.com/embed/eLtphwetAzw' },
      { id: 'pXMNNxojqHc', embedUrl: 'https://www.youtube.com/embed/pXMNNxojqHc' }
    ];

    res.status(200).json({
      shorts: fallbackShorts,
      total: fallbackShorts.length,
      fallback: true,
      error: error.message
    });
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(`Backend server running on port ${PORT}`);
});

// Export the Express API for Vercel serverless functions
module.exports = app;
