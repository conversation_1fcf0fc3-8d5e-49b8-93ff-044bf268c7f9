# css-color-keywords

> A list of all CSS color keywords.

This list contains all CSS color keywords specified in:

- [CSS Level 1](https://www.w3.org/TR/CSS1/#color-units)
- [CSS Level 2 (Revision 1)](https://www.w3.org/TR/CSS2/syndata.html#value-def-color)
- [CSS Color Module Level 3](https://drafts.csswg.org/css-color-3/#colorunits)
- [CSS Color Module Level 4](https://drafts.csswg.org/css-color/#named-colors)

[See MDN for an overview of all colors](https://developer.mozilla.org/en-US/docs/Web/CSS/color_value#Color_keywords).

## Installation

```sh
$ npm install css-color-keywords --save
```

## Usage

```js
const cssColorKeywords = require('css-color-keywords')

cssColorKeywords.black // #000000
cssColorKeywords.orange // #ffa500
```

Returns an object where each key is a keyword (e.g. `black`) and the value is
the keyword’s RGB hex value (e.g. `#000000`).

Even though CSS color keywords are case-insensitive, this list only contains
lowercase keywords (and returns lowercase RGB hex values).

## Changelog

This project follows [Semantic Versioning 2](http://semver.org/).

This list aims to always include all CSS color keywords. If new keywords are
specified, only the *minor* version will be bumped.

- v1.0.0 (2017-01-20): Initial release

## License

`css-color-keywords` is licensed under the ISC license.
See [`LICENSE`](LICENSE) for the full license.
